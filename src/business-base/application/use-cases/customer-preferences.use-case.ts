import { Inject, Injectable } from '@nestjs/common';
import { CustomerPreferencesDto } from '@business-base/application/dto/customer-preferences.dto';
import { CustomerPreferencesPort } from '@business-base/infrastructure/ports/db/customer-preferences.port';
import { logger } from '@edutalent/commons-sdk';
import {
  CustomerPreferencesEntity,
  PortfolioPreferences,
  ExportConfig,
  StatsConfig,
} from '@business-base/domain/entities/customer-preferences.entity';
import {
  BusinessException,
  BusinessExceptionStatus,
} from '@common/exception/types/BusinessException';
import { CorrelationContextService } from '@common/services/correlation-context.service';
import { RecordStatus } from '@common/enums';
import { InfraWorkflowPort } from '@business-base/infrastructure/ports/http/workflow.port';

@Injectable()
export class CustomerPreferencesUseCase {
  constructor(
    @Inject('CustomerPreferencesPort')
    private readonly customerPreferencesAdapter: CustomerPreferencesPort,
    @Inject('InfraWorkflowPort')
    private readonly workflowAdapter: InfraWorkflowPort,
  ) {}

  async create(
    customerId: string,
    createCustomerPreferencesDto: CustomerPreferencesDto,
  ): Promise<CustomerPreferencesDto> {
    const traceId = CorrelationContextService.getTraceId();

    logger.info('Creating customer preferences', {
      traceId,
      customerId,
      operation: 'createCustomerPreferences',
      layer: 'USE_CASE',
    });

    // Check if preferences already exist
    const existingPreferences = await this.customerPreferencesAdapter.getById(customerId);
    if (existingPreferences) {
      throw new BusinessException(
        'CustomerPreferencesUseCase',
        `Customer preferences already exist for customerId: ${customerId}`,
        BusinessExceptionStatus.GENERAL_ERROR,
      );
    }

    const getWorkflowIDByName = async (workflowName: string) => {
      const workflow = await this.workflowAdapter.getWorkflowByName(workflowName);
      return workflow.workflowId;
    };

    const getWorkflowNameById = async (workflowId: string) => {
      const workflow = await this.workflowAdapter.getWorkflowById(workflowId);
      return workflow.name;
    };

    // Convert DTO (with workflowName) to Entity (with workflowId)
    const portfolioPreferences = new PortfolioPreferences();

    // Copy all non-config properties first
    if (createCustomerPreferencesDto.portfolio) {
      const { exportConfig, statsConfig, ...otherProps } = createCustomerPreferencesDto.portfolio;
      Object.assign(portfolioPreferences, otherProps);
    }

    // Handle exportConfig conversion
    if (createCustomerPreferencesDto.portfolio?.exportConfig) {
      portfolioPreferences.exportConfig = [];
      for (const config of createCustomerPreferencesDto.portfolio.exportConfig) {
        const workflowId = await getWorkflowIDByName(config.workflowName);
        // Remove workflowName from config before storing, as we use workflowId as key
        const { workflowName, ...configWithoutWorkflowName } = config;
        portfolioPreferences.exportConfig.push(
          new ExportConfig(workflowId, configWithoutWorkflowName),
        );
      }
    }

    // Handle statsConfig conversion
    if (createCustomerPreferencesDto.portfolio?.statsConfig) {
      portfolioPreferences.statsConfig = [];
      for (const config of createCustomerPreferencesDto.portfolio.statsConfig) {
        const workflowId = await getWorkflowIDByName(config.workflowName);
        // Remove workflowName from config before storing, as we use workflowId as key
        const { workflowName, ...configWithoutWorkflowName } = config;
        portfolioPreferences.statsConfig.push(
          new StatsConfig(workflowId, configWithoutWorkflowName),
        );
      }
    }

    // Extract dynamic properties (excluding portfolio to avoid conflicts)
    const { status, portfolio: _portfolio, ...dynamicProperties } = createCustomerPreferencesDto;

    const entity = new CustomerPreferencesEntity({
      customerId,
      portfolio: portfolioPreferences,
      ...dynamicProperties,
    });

    const createdEntity = await this.customerPreferencesAdapter.create(entity);

    logger.info('Customer preferences created successfully', {
      traceId,
      customerId,
      operation: 'createCustomerPreferences',
      layer: 'USE_CASE',
    });

    // Convert entity back to DTO (workflowId -> workflowName)
    let portfolioDto = undefined;
    if (createdEntity.portfolio) {
      portfolioDto = { ...createdEntity.portfolio };

      // Convert exportConfig from workflowId to workflowName
      if (createdEntity.portfolio.exportConfig) {
        portfolioDto.exportConfig = await Promise.all(
          createdEntity.portfolio.exportConfig.map(async config => {
            const workflowName = await getWorkflowNameById(config.workflowId);
            const { workflowId, ...configWithoutWorkflowId } = config;
            return { workflowName, ...configWithoutWorkflowId };
          }),
        );
      }

      // Convert statsConfig from workflowId to workflowName
      if (createdEntity.portfolio.statsConfig) {
        portfolioDto.statsConfig = await Promise.all(
          createdEntity.portfolio.statsConfig.map(async config => {
            const workflowName = await getWorkflowNameById(config.workflowId);
            const { workflowId, ...configWithoutWorkflowId } = config;
            return { workflowName, ...configWithoutWorkflowId };
          }),
        );
      }
    }

    return new CustomerPreferencesDto({
      ...createdEntity, // Include all dynamic properties
      customerId,
      portfolio: portfolioDto,
    });
  }

  async findById(customerId: string): Promise<CustomerPreferencesEntity | null> {
    const traceId = CorrelationContextService.getTraceId();

    logger.info('Finding customer preferences by ID', {
      traceId,
      customerId,
      operation: 'findCustomerPreferencesById',
      layer: 'USE_CASE',
    });

    const entity = await this.customerPreferencesAdapter.getById(customerId);

    if (!entity) {
      logger.warn('Customer preferences not found', {
        traceId,
        customerId,
        operation: 'findCustomerPreferencesById',
        layer: 'USE_CASE',
      });

      return null;
    }

    logger.info('Customer preferences found successfully', {
      traceId,
      customerId,
      operation: 'findCustomerPreferencesById',
      layer: 'USE_CASE',
    });

    const getWorkflowNameById = async (workflowId: string) => {
      const workflow = await this.workflowAdapter.getWorkflowById(workflowId);
      return workflow.name;
    };

    // Convert entity back to DTO (workflowId -> workflowName)
    let portfolioDto = undefined;
    if (entity.portfolio) {
      portfolioDto = { ...entity.portfolio };

      // Convert exportConfig from workflowId to workflowName
      if (entity.portfolio.exportConfig) {
        portfolioDto.exportConfig = await Promise.all(
          entity.portfolio.exportConfig.map(async config => {
            const workflowName = await getWorkflowNameById(config.workflowId);
            const { workflowId, ...configWithoutWorkflowId } = config;
            return { workflowName, ...configWithoutWorkflowId };
          }),
        );
      }

      // Convert statsConfig from workflowId to workflowName
      if (entity.portfolio.statsConfig) {
        portfolioDto.statsConfig = await Promise.all(
          entity.portfolio.statsConfig.map(async config => {
            const workflowName = await getWorkflowNameById(config.workflowId);
            const { workflowId, ...configWithoutWorkflowId } = config;
            return { workflowName, ...configWithoutWorkflowId };
          }),
        );
      }
    }

    return new CustomerPreferencesEntity({
      ...entity, // Include all dynamic properties
      customerId,
      portfolio: portfolioDto,
    });
  }

  async update(
    customerId: string,
    updateCustomerPreferencesDto: CustomerPreferencesDto,
  ): Promise<CustomerPreferencesDto> {
    const traceId = CorrelationContextService.getTraceId();

    logger.info('Updating customer preferences', {
      traceId,
      customerId,
      operation: 'updateCustomerPreferences',
      layer: 'USE_CASE',
    });

    // Get existing preferences
    const existingEntity = await this.customerPreferencesAdapter.getByIdIgnoreStatus(customerId);
    if (!existingEntity) {
      throw new BusinessException(
        'CustomerPreferencesUseCase',
        `Customer preferences not found for customerId: ${customerId}`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    // Apply PUT semantics - replace with provided data only
    let updatedPortfolio = undefined;
    if (updateCustomerPreferencesDto.portfolio) {
      const getWorkflowIDByName = async (workflowName: string) => {
        const workflow = await this.workflowAdapter.getWorkflowByName(workflowName);
        return workflow.workflowId;
      };

      // Convert DTO (with workflowName) to Entity (with workflowId)
      updatedPortfolio = new PortfolioPreferences();

      // Copy all non-config properties first
      const { exportConfig, statsConfig, ...otherProps } = updateCustomerPreferencesDto.portfolio;
      Object.assign(updatedPortfolio, otherProps);

      // Handle exportConfig conversion
      if (updateCustomerPreferencesDto.portfolio.exportConfig) {
        updatedPortfolio.exportConfig = [];
        for (const config of updateCustomerPreferencesDto.portfolio.exportConfig) {
          const workflowId = await getWorkflowIDByName(config.workflowName);
          // Remove workflowName from config before storing, as we use workflowId as key
          const { workflowName, ...configWithoutWorkflowName } = config;
          updatedPortfolio.exportConfig.push(
            new ExportConfig(workflowId, configWithoutWorkflowName),
          );
        }
      }

      // Handle statsConfig conversion
      if (updateCustomerPreferencesDto.portfolio.statsConfig) {
        updatedPortfolio.statsConfig = [];
        for (const config of updateCustomerPreferencesDto.portfolio.statsConfig) {
          const workflowId = await getWorkflowIDByName(config.workflowName);
          // Remove workflowName from config before storing, as we use workflowId as key
          const { workflowName, ...configWithoutWorkflowName } = config;
          updatedPortfolio.statsConfig.push(new StatsConfig(workflowId, configWithoutWorkflowName));
        }
      }
    }

    // Create updated entity with PUT semantics (replace entire resource)
    const { portfolio: _portfolio, ...dynamicProperties } = updateCustomerPreferencesDto;
    let status = updateCustomerPreferencesDto.status;

    if (!status) {
      status = RecordStatus.ACTIVE; // Default to ACTIVE if not provided
    }

    const updatedEntity = new CustomerPreferencesEntity({
      // Only include properties from the payload (PUT semantics)
      ...dynamicProperties,
      customerId,
      portfolio: updatedPortfolio,
      // Keep only essential system properties from existing entity
      status: status === RecordStatus.ACTIVE ? RecordStatus.ACTIVE : RecordStatus.DELETED,
      createdAt: existingEntity.createdAt,
      updatedAt: new Date(),
    });

    const result = await this.customerPreferencesAdapter.update(customerId, updatedEntity);

    logger.info('Customer preferences updated successfully', {
      traceId,
      customerId,
      operation: 'updateCustomerPreferences',
      layer: 'USE_CASE',
    });

    const getWorkflowNameById = async (workflowId: string) => {
      const workflow = await this.workflowAdapter.getWorkflowById(workflowId);
      return workflow.name;
    };

    // Convert entity back to DTO (workflowId -> workflowName)
    let portfolioDto = undefined;
    if (result.portfolio) {
      portfolioDto = { ...result.portfolio };

      // Convert exportConfig from workflowId to workflowName
      if (result.portfolio.exportConfig) {
        portfolioDto.exportConfig = await Promise.all(
          result.portfolio.exportConfig.map(async config => {
            const workflowName = await getWorkflowNameById(config.workflowId);
            const { workflowId, ...configWithoutWorkflowId } = config;
            return { workflowName, ...configWithoutWorkflowId };
          }),
        );
      }

      // Convert statsConfig from workflowId to workflowName
      if (result.portfolio.statsConfig) {
        portfolioDto.statsConfig = await Promise.all(
          result.portfolio.statsConfig.map(async config => {
            const workflowName = await getWorkflowNameById(config.workflowId);
            const { workflowId, ...configWithoutWorkflowId } = config;
            return { workflowName, ...configWithoutWorkflowId };
          }),
        );
      }
    }

    return new CustomerPreferencesDto({
      ...result, // Include all dynamic properties
      customerId,
      portfolio: portfolioDto,
    });
  }

  async delete(customerId: string): Promise<void> {
    const traceId = CorrelationContextService.getTraceId();

    logger.info('Deleting customer preferences', {
      traceId,
      customerId,
      operation: 'deleteCustomerPreferences',
      layer: 'USE_CASE',
    });

    // Check if preferences exist
    const existingEntity = await this.customerPreferencesAdapter.getById(customerId);
    if (!existingEntity) {
      throw new BusinessException(
        'CustomerPreferencesUseCase',
        `Customer preferences not found for customerId: ${customerId}`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    await this.customerPreferencesAdapter.delete(customerId);

    logger.info('Customer preferences deleted successfully', {
      traceId,
      customerId,
      operation: 'deleteCustomerPreferences',
      layer: 'USE_CASE',
    });
  }
}
